{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/md.transition.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation.js';\nimport { g as getIonPageElement } from './index2.js';\n\nconst mdTransitionAnimation = (_, opts) => {\n    var _a, _b, _c;\n    const OFF_BOTTOM = '40px';\n    const CENTER = '0px';\n    const backDirection = opts.direction === 'back';\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const ionPageElement = getIonPageElement(enteringEl);\n    const enteringToolbarEle = ionPageElement.querySelector('ion-toolbar');\n    const rootTransition = createAnimation();\n    rootTransition.addElement(ionPageElement).fill('both').beforeRemoveClass('ion-page-invisible');\n    // animate the component itself\n    if (backDirection) {\n        rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n    }\n    else {\n        rootTransition\n            .duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280)\n            .easing('cubic-bezier(0.36,0.66,0.04,1)')\n            .fromTo('transform', `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`)\n            .fromTo('opacity', 0.01, 1);\n    }\n    // Animate toolbar if it's there\n    if (enteringToolbarEle) {\n        const enteringToolBar = createAnimation();\n        enteringToolBar.addElement(enteringToolbarEle);\n        rootTransition.addAnimation(enteringToolBar);\n    }\n    // setup leaving view\n    if (leavingEl && backDirection) {\n        // leaving content\n        rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n        const leavingPage = createAnimation();\n        leavingPage\n            .addElement(getIonPageElement(leavingEl))\n            .onFinish((currentStep) => {\n            if (currentStep === 1 && leavingPage.elements.length > 0) {\n                leavingPage.elements[0].style.setProperty('display', 'none');\n            }\n        })\n            .fromTo('transform', `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`)\n            .fromTo('opacity', 1, 0);\n        rootTransition.addAnimation(leavingPage);\n    }\n    return rootTransition;\n};\n\nexport { mdTransitionAnimation };\n"], "mappings": ";;;;;;AAMA,IAAM,wBAAwB,CAAC,GAAG,SAAS;AACvC,MAAI,IAAI,IAAI;AACZ,QAAM,aAAa;AACnB,QAAM,SAAS;AACf,QAAM,gBAAgB,KAAK,cAAc;AACzC,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,QAAM,iBAAiB,kBAAkB,UAAU;AACnD,QAAM,qBAAqB,eAAe,cAAc,aAAa;AACrE,QAAM,iBAAiB,gBAAgB;AACvC,iBAAe,WAAW,cAAc,EAAE,KAAK,MAAM,EAAE,kBAAkB,oBAAoB;AAE7F,MAAI,eAAe;AACf,mBAAe,WAAW,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,OAAO,kCAAkC;AAAA,EACvI,OACK;AACD,mBACK,WAAW,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,MAAM,GAAG,EACzE,OAAO,gCAAgC,EACvC,OAAO,aAAa,cAAc,UAAU,KAAK,cAAc,MAAM,GAAG,EACxE,OAAO,WAAW,MAAM,CAAC;AAAA,EAClC;AAEA,MAAI,oBAAoB;AACpB,UAAM,kBAAkB,gBAAgB;AACxC,oBAAgB,WAAW,kBAAkB;AAC7C,mBAAe,aAAa,eAAe;AAAA,EAC/C;AAEA,MAAI,aAAa,eAAe;AAE5B,mBAAe,WAAW,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,OAAO,kCAAkC;AACnI,UAAM,cAAc,gBAAgB;AACpC,gBACK,WAAW,kBAAkB,SAAS,CAAC,EACvC,SAAS,CAAC,gBAAgB;AAC3B,UAAI,gBAAgB,KAAK,YAAY,SAAS,SAAS,GAAG;AACtD,oBAAY,SAAS,CAAC,EAAE,MAAM,YAAY,WAAW,MAAM;AAAA,MAC/D;AAAA,IACJ,CAAC,EACI,OAAO,aAAa,cAAc,MAAM,KAAK,cAAc,UAAU,GAAG,EACxE,OAAO,WAAW,GAAG,CAAC;AAC3B,mBAAe,aAAa,WAAW;AAAA,EAC3C;AACA,SAAO;AACX;", "names": []}