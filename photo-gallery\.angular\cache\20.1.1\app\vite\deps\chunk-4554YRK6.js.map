{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/helpers.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { a as printIonError } from './index4.js';\n\nconst transitionEndAsync = (el, expectedDuration = 0) => {\n    return new Promise((resolve) => {\n        transitionEnd(el, expectedDuration, resolve);\n    });\n};\n/**\n * Allows developer to wait for a transition\n * to finish and fallback to a timer if the\n * transition is cancelled or otherwise\n * never finishes. Also see transitionEndAsync\n * which is an await-able version of this.\n */\nconst transitionEnd = (el, expectedDuration = 0, callback) => {\n    let unRegTrans;\n    let animationTimeout;\n    const opts = { passive: true };\n    const ANIMATION_FALLBACK_TIMEOUT = 500;\n    const unregister = () => {\n        if (unRegTrans) {\n            unRegTrans();\n        }\n    };\n    const onTransitionEnd = (ev) => {\n        if (ev === undefined || el === ev.target) {\n            unregister();\n            callback(ev);\n        }\n    };\n    if (el) {\n        el.addEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n        el.addEventListener('transitionend', onTransitionEnd, opts);\n        animationTimeout = setTimeout(onTransitionEnd, expectedDuration + ANIMATION_FALLBACK_TIMEOUT);\n        unRegTrans = () => {\n            if (animationTimeout !== undefined) {\n                clearTimeout(animationTimeout);\n                animationTimeout = undefined;\n            }\n            el.removeEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n            el.removeEventListener('transitionend', onTransitionEnd, opts);\n        };\n    }\n    return unregister;\n};\n/**\n * Waits for a component to be ready for\n * both custom element and non-custom element builds.\n * If non-custom element build, el.componentOnReady\n * will be used.\n * For custom element builds, we wait a frame\n * so that the inner contents of the component\n * have a chance to render.\n *\n * Use this utility rather than calling\n * el.componentOnReady yourself.\n */\nconst componentOnReady = (el, callback) => {\n    if (el.componentOnReady) {\n        // eslint-disable-next-line custom-rules/no-component-on-ready-method\n        el.componentOnReady().then((resolvedEl) => callback(resolvedEl));\n    }\n    else {\n        raf(() => callback(el));\n    }\n};\n/**\n * This functions checks if a Stencil component is using\n * the lazy loaded build of Stencil. Returns `true` if\n * the component is lazy loaded. Returns `false` otherwise.\n */\nconst hasLazyBuild = (stencilEl) => {\n    return stencilEl.componentOnReady !== undefined;\n};\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n    const attributeObject = {};\n    attributes.forEach((attr) => {\n        if (el.hasAttribute(attr)) {\n            const value = el.getAttribute(attr);\n            if (value !== null) {\n                attributeObject[attr] = el.getAttribute(attr);\n            }\n            el.removeAttribute(attr);\n        }\n    });\n    return attributeObject;\n};\n/**\n * List of available ARIA attributes + `role`.\n * Removed deprecated attributes.\n * https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes\n */\nconst ariaAttributes = [\n    'role',\n    'aria-activedescendant',\n    'aria-atomic',\n    'aria-autocomplete',\n    'aria-braillelabel',\n    'aria-brailleroledescription',\n    'aria-busy',\n    'aria-checked',\n    'aria-colcount',\n    'aria-colindex',\n    'aria-colindextext',\n    'aria-colspan',\n    'aria-controls',\n    'aria-current',\n    'aria-describedby',\n    'aria-description',\n    'aria-details',\n    'aria-disabled',\n    'aria-errormessage',\n    'aria-expanded',\n    'aria-flowto',\n    'aria-haspopup',\n    'aria-hidden',\n    'aria-invalid',\n    'aria-keyshortcuts',\n    'aria-label',\n    'aria-labelledby',\n    'aria-level',\n    'aria-live',\n    'aria-multiline',\n    'aria-multiselectable',\n    'aria-orientation',\n    'aria-owns',\n    'aria-placeholder',\n    'aria-posinset',\n    'aria-pressed',\n    'aria-readonly',\n    'aria-relevant',\n    'aria-required',\n    'aria-roledescription',\n    'aria-rowcount',\n    'aria-rowindex',\n    'aria-rowindextext',\n    'aria-rowspan',\n    'aria-selected',\n    'aria-setsize',\n    'aria-sort',\n    'aria-valuemax',\n    'aria-valuemin',\n    'aria-valuenow',\n    'aria-valuetext',\n];\n/**\n * Returns an array of aria attributes that should be copied from\n * the shadow host element to a target within the light DOM.\n * @param el The element that the attributes should be copied from.\n * @param ignoreList The list of aria-attributes to ignore reflecting and removing from the host.\n * Use this in instances where we manually specify aria attributes on the `<Host>` element.\n */\nconst inheritAriaAttributes = (el, ignoreList) => {\n    let attributesToInherit = ariaAttributes;\n    return inheritAttributes(el, attributesToInherit);\n};\nconst addEventListener = (el, eventName, callback, opts) => {\n    return el.addEventListener(eventName, callback, opts);\n};\nconst removeEventListener = (el, eventName, callback, opts) => {\n    return el.removeEventListener(eventName, callback, opts);\n};\n/**\n * Gets the root context of a shadow dom element\n * On newer browsers this will be the shadowRoot,\n * but for older browser this may just be the\n * element itself.\n *\n * Useful for whenever you need to explicitly\n * do \"myElement.shadowRoot!.querySelector(...)\".\n */\nconst getElementRoot = (el, fallback = el) => {\n    return el.shadowRoot || fallback;\n};\n/**\n * Patched version of requestAnimationFrame that avoids ngzone\n * Use only when you know ngzone should not run\n */\nconst raf = (h) => {\n    if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n        return __zone_symbol__requestAnimationFrame(h);\n    }\n    if (typeof requestAnimationFrame === 'function') {\n        return requestAnimationFrame(h);\n    }\n    return setTimeout(h);\n};\nconst hasShadowDom = (el) => {\n    return !!el.shadowRoot && !!el.attachShadow;\n};\nconst focusVisibleElement = (el) => {\n    el.focus();\n    /**\n     * When programmatically focusing an element,\n     * the focus-visible utility will not run because\n     * it is expecting a keyboard event to have triggered this;\n     * however, there are times when we need to manually control\n     * this behavior so we call the `setFocus` method on ion-app\n     * which will let us explicitly set the elements to focus.\n     */\n    if (el.classList.contains('ion-focusable')) {\n        const app = el.closest('ion-app');\n        if (app) {\n            app.setFocus([el]);\n        }\n    }\n};\n/**\n * This method is used to add a hidden input to a host element that contains\n * a Shadow DOM. It does not add the input inside of the Shadow root which\n * allows it to be picked up inside of forms. It should contain the same\n * values as the host element.\n *\n * @param always Add a hidden input even if the container does not use Shadow\n * @param container The element where the input will be added\n * @param name The name of the input\n * @param value The value of the input\n * @param disabled If true, the input is disabled\n */\nconst renderHiddenInput = (always, container, name, value, disabled) => {\n    {\n        let input = container.querySelector('input.aux-input');\n        if (!input) {\n            input = container.ownerDocument.createElement('input');\n            input.type = 'hidden';\n            input.classList.add('aux-input');\n            container.appendChild(input);\n        }\n        input.disabled = disabled;\n        input.name = name;\n        input.value = value || '';\n    }\n};\nconst clamp = (min, n, max) => {\n    return Math.max(min, Math.min(n, max));\n};\nconst assert = (actual, reason) => {\n    if (!actual) {\n        const message = 'ASSERT: ' + reason;\n        printIonError(message);\n        debugger; // eslint-disable-line\n        throw new Error(message);\n    }\n};\nconst pointerCoord = (ev) => {\n    // get X coordinates for either a mouse click\n    // or a touch depending on the given event\n    if (ev) {\n        const changedTouches = ev.changedTouches;\n        if (changedTouches && changedTouches.length > 0) {\n            const touch = changedTouches[0];\n            return { x: touch.clientX, y: touch.clientY };\n        }\n        if (ev.pageX !== undefined) {\n            return { x: ev.pageX, y: ev.pageY };\n        }\n    }\n    return { x: 0, y: 0 };\n};\n/**\n * @hidden\n * Given a side, return if it should be on the end\n * based on the value of dir\n * @param side the side\n * @param isRTL whether the application dir is rtl\n */\nconst isEndSide = (side) => {\n    const isRTL = document.dir === 'rtl';\n    switch (side) {\n        case 'start':\n            return isRTL;\n        case 'end':\n            return !isRTL;\n        default:\n            throw new Error(`\"${side}\" is not a valid value for [side]. Use \"start\" or \"end\" instead.`);\n    }\n};\nconst debounceEvent = (event, wait) => {\n    const original = event._original || event;\n    return {\n        _original: event,\n        emit: debounce(original.emit.bind(original), wait),\n    };\n};\nconst debounce = (func, wait = 0) => {\n    let timer;\n    return (...args) => {\n        clearTimeout(timer);\n        timer = setTimeout(func, wait, ...args);\n    };\n};\n/**\n * Check whether the two string maps are shallow equal.\n *\n * undefined is treated as an empty map.\n *\n * @returns whether the keys are the same and the values are shallow equal.\n */\nconst shallowEqualStringMap = (map1, map2) => {\n    map1 !== null && map1 !== void 0 ? map1 : (map1 = {});\n    map2 !== null && map2 !== void 0 ? map2 : (map2 = {});\n    if (map1 === map2) {\n        return true;\n    }\n    const keys1 = Object.keys(map1);\n    if (keys1.length !== Object.keys(map2).length) {\n        return false;\n    }\n    for (const k1 of keys1) {\n        if (!(k1 in map2)) {\n            return false;\n        }\n        if (map1[k1] !== map2[k1]) {\n            return false;\n        }\n    }\n    return true;\n};\n/**\n * Checks input for usable number. Not NaN and not Infinite.\n */\nconst isSafeNumber = (input) => {\n    return typeof input === 'number' && !isNaN(input) && isFinite(input);\n};\n\nexport { addEventListener as a, removeEventListener as b, componentOnReady as c, inheritAttributes as d, renderHiddenInput as e, clamp as f, getElementRoot as g, focusVisibleElement as h, inheritAriaAttributes as i, hasShadowDom as j, hasLazyBuild as k, debounceEvent as l, isEndSide as m, assert as n, isSafeNumber as o, debounce as p, pointerCoord as q, raf as r, shallowEqualStringMap as s, transitionEndAsync as t };\n"], "mappings": ";;;;;AAKA,IAAM,qBAAqB,CAAC,IAAI,mBAAmB,MAAM;AACrD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,kBAAc,IAAI,kBAAkB,OAAO;AAAA,EAC/C,CAAC;AACL;AAQA,IAAM,gBAAgB,CAAC,IAAI,mBAAmB,GAAG,aAAa;AAC1D,MAAI;AACJ,MAAI;AACJ,QAAM,OAAO,EAAE,SAAS,KAAK;AAC7B,QAAM,6BAA6B;AACnC,QAAM,aAAa,MAAM;AACrB,QAAI,YAAY;AACZ,iBAAW;AAAA,IACf;AAAA,EACJ;AACA,QAAM,kBAAkB,CAAC,OAAO;AAC5B,QAAI,OAAO,UAAa,OAAO,GAAG,QAAQ;AACtC,iBAAW;AACX,eAAS,EAAE;AAAA,IACf;AAAA,EACJ;AACA,MAAI,IAAI;AACJ,OAAG,iBAAiB,uBAAuB,iBAAiB,IAAI;AAChE,OAAG,iBAAiB,iBAAiB,iBAAiB,IAAI;AAC1D,uBAAmB,WAAW,iBAAiB,mBAAmB,0BAA0B;AAC5F,iBAAa,MAAM;AACf,UAAI,qBAAqB,QAAW;AAChC,qBAAa,gBAAgB;AAC7B,2BAAmB;AAAA,MACvB;AACA,SAAG,oBAAoB,uBAAuB,iBAAiB,IAAI;AACnE,SAAG,oBAAoB,iBAAiB,iBAAiB,IAAI;AAAA,IACjE;AAAA,EACJ;AACA,SAAO;AACX;AAaA,IAAM,mBAAmB,CAAC,IAAI,aAAa;AACvC,MAAI,GAAG,kBAAkB;AAErB,OAAG,iBAAiB,EAAE,KAAK,CAAC,eAAe,SAAS,UAAU,CAAC;AAAA,EACnE,OACK;AACD,QAAI,MAAM,SAAS,EAAE,CAAC;AAAA,EAC1B;AACJ;AAMA,IAAM,eAAe,CAAC,cAAc;AAChC,SAAO,UAAU,qBAAqB;AAC1C;AAWA,IAAM,oBAAoB,CAAC,IAAI,aAAa,CAAC,MAAM;AAC/C,QAAM,kBAAkB,CAAC;AACzB,aAAW,QAAQ,CAAC,SAAS;AACzB,QAAI,GAAG,aAAa,IAAI,GAAG;AACvB,YAAM,QAAQ,GAAG,aAAa,IAAI;AAClC,UAAI,UAAU,MAAM;AAChB,wBAAgB,IAAI,IAAI,GAAG,aAAa,IAAI;AAAA,MAChD;AACA,SAAG,gBAAgB,IAAI;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAMA,IAAM,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAQA,IAAM,wBAAwB,CAAC,IAAI,eAAe;AAC9C,MAAI,sBAAsB;AAC1B,SAAO,kBAAkB,IAAI,mBAAmB;AACpD;AACA,IAAM,mBAAmB,CAAC,IAAI,WAAW,UAAU,SAAS;AACxD,SAAO,GAAG,iBAAiB,WAAW,UAAU,IAAI;AACxD;AACA,IAAM,sBAAsB,CAAC,IAAI,WAAW,UAAU,SAAS;AAC3D,SAAO,GAAG,oBAAoB,WAAW,UAAU,IAAI;AAC3D;AAUA,IAAM,iBAAiB,CAAC,IAAI,WAAW,OAAO;AAC1C,SAAO,GAAG,cAAc;AAC5B;AAKA,IAAM,MAAM,CAAC,MAAM;AACf,MAAI,OAAO,yCAAyC,YAAY;AAC5D,WAAO,qCAAqC,CAAC;AAAA,EACjD;AACA,MAAI,OAAO,0BAA0B,YAAY;AAC7C,WAAO,sBAAsB,CAAC;AAAA,EAClC;AACA,SAAO,WAAW,CAAC;AACvB;AACA,IAAM,eAAe,CAAC,OAAO;AACzB,SAAO,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;AACnC;AACA,IAAM,sBAAsB,CAAC,OAAO;AAChC,KAAG,MAAM;AAST,MAAI,GAAG,UAAU,SAAS,eAAe,GAAG;AACxC,UAAM,MAAM,GAAG,QAAQ,SAAS;AAChC,QAAI,KAAK;AACL,UAAI,SAAS,CAAC,EAAE,CAAC;AAAA,IACrB;AAAA,EACJ;AACJ;AAaA,IAAM,oBAAoB,CAAC,QAAQ,WAAW,MAAM,OAAO,aAAa;AACpE;AACI,QAAI,QAAQ,UAAU,cAAc,iBAAiB;AACrD,QAAI,CAAC,OAAO;AACR,cAAQ,UAAU,cAAc,cAAc,OAAO;AACrD,YAAM,OAAO;AACb,YAAM,UAAU,IAAI,WAAW;AAC/B,gBAAU,YAAY,KAAK;AAAA,IAC/B;AACA,UAAM,WAAW;AACjB,UAAM,OAAO;AACb,UAAM,QAAQ,SAAS;AAAA,EAC3B;AACJ;AACA,IAAM,QAAQ,CAAC,KAAK,GAAG,QAAQ;AAC3B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AACzC;AACA,IAAM,SAAS,CAAC,QAAQ,WAAW;AAC/B,MAAI,CAAC,QAAQ;AACT,UAAM,UAAU,aAAa;AAC7B,kBAAc,OAAO;AACrB;AACA,UAAM,IAAI,MAAM,OAAO;AAAA,EAC3B;AACJ;AACA,IAAM,eAAe,CAAC,OAAO;AAGzB,MAAI,IAAI;AACJ,UAAM,iBAAiB,GAAG;AAC1B,QAAI,kBAAkB,eAAe,SAAS,GAAG;AAC7C,YAAM,QAAQ,eAAe,CAAC;AAC9B,aAAO,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AAAA,IAChD;AACA,QAAI,GAAG,UAAU,QAAW;AACxB,aAAO,EAAE,GAAG,GAAG,OAAO,GAAG,GAAG,MAAM;AAAA,IACtC;AAAA,EACJ;AACA,SAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACxB;AAQA,IAAM,YAAY,CAAC,SAAS;AACxB,QAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,CAAC;AAAA,IACZ;AACI,YAAM,IAAI,MAAM,IAAI,IAAI,kEAAkE;AAAA,EAClG;AACJ;AACA,IAAM,gBAAgB,CAAC,OAAO,SAAS;AACnC,QAAM,WAAW,MAAM,aAAa;AACpC,SAAO;AAAA,IACH,WAAW;AAAA,IACX,MAAM,SAAS,SAAS,KAAK,KAAK,QAAQ,GAAG,IAAI;AAAA,EACrD;AACJ;AACA,IAAM,WAAW,CAAC,MAAM,OAAO,MAAM;AACjC,MAAI;AACJ,SAAO,IAAI,SAAS;AAChB,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM,MAAM,GAAG,IAAI;AAAA,EAC1C;AACJ;AAQA,IAAM,wBAAwB,CAAC,MAAM,SAAS;AAC1C,WAAS,QAAQ,SAAS,SAAS,OAAQ,OAAO,CAAC;AACnD,WAAS,QAAQ,SAAS,SAAS,OAAQ,OAAO,CAAC;AACnD,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC3C,WAAO;AAAA,EACX;AACA,aAAW,MAAM,OAAO;AACpB,QAAI,EAAE,MAAM,OAAO;AACf,aAAO;AAAA,IACX;AACA,QAAI,KAAK,EAAE,MAAM,KAAK,EAAE,GAAG;AACvB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAIA,IAAM,eAAe,CAAC,UAAU;AAC5B,SAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,KAAK,SAAS,KAAK;AACvE;", "names": []}