{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/index8.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers.js';\nimport { e as printRequiredElementError } from './index4.js';\n\nconst ION_CONTENT_TAG_NAME = 'ION-CONTENT';\nconst ION_CONTENT_ELEMENT_SELECTOR = 'ion-content';\nconst ION_CONTENT_CLASS_SELECTOR = '.ion-content-scroll-host';\n/**\n * Selector used for implementations reliant on `<ion-content>` for scroll event changes.\n *\n * Developers should use the `.ion-content-scroll-host` selector to target the element emitting\n * scroll events. With virtual scroll implementations this will be the host element for\n * the scroll viewport.\n */\nconst ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;\nconst isIonContent = (el) => el.tagName === ION_CONTENT_TAG_NAME;\n/**\n * Waits for the element host fully initialize before\n * returning the inner scroll element.\n *\n * For `ion-content` the scroll target will be the result\n * of the `getScrollElement` function.\n *\n * For custom implementations it will be the element host\n * or a selector within the host, if supplied through `scrollTarget`.\n */\nconst getScrollElement = async (el) => {\n    if (isIonContent(el)) {\n        await new Promise((resolve) => componentOnReady(el, resolve));\n        return el.getScrollElement();\n    }\n    return el;\n};\n/**\n * Queries the element matching the selector for IonContent.\n * See ION_CONTENT_SELECTOR for the selector used.\n */\nconst findIonContent = (el) => {\n    /**\n     * First we try to query the custom scroll host selector in cases where\n     * the implementation is using an outer `ion-content` with an inner custom\n     * scroll container.\n     */\n    const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);\n    if (customContentHost) {\n        return customContentHost;\n    }\n    return el.querySelector(ION_CONTENT_SELECTOR);\n};\n/**\n * Queries the closest element matching the selector for IonContent.\n */\nconst findClosestIonContent = (el) => {\n    return el.closest(ION_CONTENT_SELECTOR);\n};\n/**\n * Scrolls to the top of the element. If an `ion-content` is found, it will scroll\n * using the public API `scrollToTop` with a duration.\n */\nconst scrollToTop = (el, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollToTop(durationMs);\n    }\n    return Promise.resolve(el.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: 'smooth' ,\n    }));\n};\n/**\n * Scrolls by a specified X/Y distance in the component. If an `ion-content` is found, it will scroll\n * using the public API `scrollByPoint` with a duration.\n */\nconst scrollByPoint = (el, x, y, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollByPoint(x, y, durationMs);\n    }\n    return Promise.resolve(el.scrollBy({\n        top: y,\n        left: x,\n        behavior: durationMs > 0 ? 'smooth' : 'auto',\n    }));\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within either the `ion-content` selector or the `.ion-content-scroll-host` class.\n */\nconst printIonContentErrorMsg = (el) => {\n    return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);\n};\n/**\n * Several components in Ionic need to prevent scrolling\n * during a gesture (card modal, range, item sliding, etc).\n * Use this utility to account for ion-content and custom content hosts.\n */\nconst disableContentScrollY = (contentEl) => {\n    if (isIonContent(contentEl)) {\n        const ionContent = contentEl;\n        const initialScrollY = ionContent.scrollY;\n        ionContent.scrollY = false;\n        /**\n         * This should be passed into resetContentScrollY\n         * so that we can revert ion-content's scrollY to the\n         * correct state. For example, if scrollY = false\n         * initially, we do not want to enable scrolling\n         * when we call resetContentScrollY.\n         */\n        return initialScrollY;\n    }\n    else {\n        contentEl.style.setProperty('overflow', 'hidden');\n        return true;\n    }\n};\nconst resetContentScrollY = (contentEl, initialScrollY) => {\n    if (isIonContent(contentEl)) {\n        contentEl.scrollY = initialScrollY;\n    }\n    else {\n        contentEl.style.removeProperty('overflow');\n    }\n};\n\nexport { ION_CONTENT_CLASS_SELECTOR as I, findClosestIonContent as a, ION_CONTENT_ELEMENT_SELECTOR as b, scrollByPoint as c, disableContentScrollY as d, findIonContent as f, getScrollElement as g, isIonContent as i, printIonContentErrorMsg as p, resetContentScrollY as r, scrollToTop as s };\n"], "mappings": ";;;;;;;;;;;AAMA,IAAM,uBAAuB;AAC7B,IAAM,+BAA+B;AACrC,IAAM,6BAA6B;AAQnC,IAAM,uBAAuB,GAAG,4BAA4B,KAAK,0BAA0B;AAC3F,IAAM,eAAe,CAAC,OAAO,GAAG,YAAY;AAW5C,IAAM,mBAAmB,CAAO,OAAO;AACnC,MAAI,aAAa,EAAE,GAAG;AAClB,UAAM,IAAI,QAAQ,CAAC,YAAY,iBAAiB,IAAI,OAAO,CAAC;AAC5D,WAAO,GAAG,iBAAiB;AAAA,EAC/B;AACA,SAAO;AACX;AAKA,IAAM,iBAAiB,CAAC,OAAO;AAM3B,QAAM,oBAAoB,GAAG,cAAc,0BAA0B;AACrE,MAAI,mBAAmB;AACnB,WAAO;AAAA,EACX;AACA,SAAO,GAAG,cAAc,oBAAoB;AAChD;AAIA,IAAM,wBAAwB,CAAC,OAAO;AAClC,SAAO,GAAG,QAAQ,oBAAoB;AAC1C;AAKA,IAAM,cAAc,CAAC,IAAI,eAAe;AACpC,MAAI,aAAa,EAAE,GAAG;AAClB,UAAM,UAAU;AAChB,WAAO,QAAQ,YAAY,UAAU;AAAA,EACzC;AACA,SAAO,QAAQ,QAAQ,GAAG,SAAS;AAAA,IAC/B,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,EACd,CAAC,CAAC;AACN;AAKA,IAAM,gBAAgB,CAAC,IAAI,GAAG,GAAG,eAAe;AAC5C,MAAI,aAAa,EAAE,GAAG;AAClB,UAAM,UAAU;AAChB,WAAO,QAAQ,cAAc,GAAG,GAAG,UAAU;AAAA,EACjD;AACA,SAAO,QAAQ,QAAQ,GAAG,SAAS;AAAA,IAC/B,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU,aAAa,IAAI,WAAW;AAAA,EAC1C,CAAC,CAAC;AACN;AAKA,IAAM,0BAA0B,CAAC,OAAO;AACpC,SAAO,0BAA0B,IAAI,4BAA4B;AACrE;AAMA,IAAM,wBAAwB,CAAC,cAAc;AACzC,MAAI,aAAa,SAAS,GAAG;AACzB,UAAM,aAAa;AACnB,UAAM,iBAAiB,WAAW;AAClC,eAAW,UAAU;AAQrB,WAAO;AAAA,EACX,OACK;AACD,cAAU,MAAM,YAAY,YAAY,QAAQ;AAChD,WAAO;AAAA,EACX;AACJ;AACA,IAAM,sBAAsB,CAAC,WAAW,mBAAmB;AACvD,MAAI,aAAa,SAAS,GAAG;AACzB,cAAU,UAAU;AAAA,EACxB,OACK;AACD,cAAU,MAAM,eAAe,UAAU;AAAA,EAC7C;AACJ;", "names": []}