{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/index7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index9.js';\nimport { q as pointerCoord } from './helpers.js';\n\nconst startTapClick = (config) => {\n    if (doc === undefined) {\n        return;\n    }\n    let lastActivated = 0;\n    let activatableEle;\n    let activeRipple;\n    let activeDefer;\n    const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n    const clearDefers = new WeakMap();\n    const cancelActive = () => {\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        if (activatableEle) {\n            removeActivated(false);\n            activatableEle = undefined;\n        }\n    };\n    const pointerDown = (ev) => {\n        // Ignore right clicks\n        if (activatableEle || ev.button === 2) {\n            return;\n        }\n        setActivatedElement(getActivatableTarget(ev), ev);\n    };\n    const pointerUp = (ev) => {\n        setActivatedElement(undefined, ev);\n    };\n    const setActivatedElement = (el, ev) => {\n        // do nothing\n        if (el && el === activatableEle) {\n            return;\n        }\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        const { x, y } = pointerCoord(ev);\n        // deactivate selected\n        if (activatableEle) {\n            if (clearDefers.has(activatableEle)) {\n                throw new Error('internal error');\n            }\n            if (!activatableEle.classList.contains(ACTIVATED)) {\n                addActivated(activatableEle, x, y);\n            }\n            removeActivated(true);\n        }\n        // activate\n        if (el) {\n            const deferId = clearDefers.get(el);\n            if (deferId) {\n                clearTimeout(deferId);\n                clearDefers.delete(el);\n            }\n            el.classList.remove(ACTIVATED);\n            const callback = () => {\n                addActivated(el, x, y);\n                activeDefer = undefined;\n            };\n            if (isInstant(el)) {\n                callback();\n            }\n            else {\n                activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n            }\n        }\n        activatableEle = el;\n    };\n    const addActivated = (el, x, y) => {\n        lastActivated = Date.now();\n        el.classList.add(ACTIVATED);\n        if (!useRippleEffect)\n            return;\n        const rippleEffect = getRippleEffect(el);\n        if (rippleEffect !== null) {\n            removeRipple();\n            activeRipple = rippleEffect.addRipple(x, y);\n        }\n    };\n    const removeRipple = () => {\n        if (activeRipple !== undefined) {\n            activeRipple.then((remove) => remove());\n            activeRipple = undefined;\n        }\n    };\n    const removeActivated = (smooth) => {\n        removeRipple();\n        const active = activatableEle;\n        if (!active) {\n            return;\n        }\n        const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n        if (smooth && time > 0 && !isInstant(active)) {\n            const deferId = setTimeout(() => {\n                active.classList.remove(ACTIVATED);\n                clearDefers.delete(active);\n            }, CLEAR_STATE_DEFERS);\n            clearDefers.set(active, deferId);\n        }\n        else {\n            active.classList.remove(ACTIVATED);\n        }\n    };\n    doc.addEventListener('ionGestureCaptured', cancelActive);\n    doc.addEventListener('pointerdown', pointerDown, true);\n    doc.addEventListener('pointerup', pointerUp, true);\n    /**\n     * Tap click effects such as the ripple effect should\n     * not happen when scrolling. For example, if a user scrolls\n     * the page but also happens to do a touchstart on a button\n     * as part of the scroll, the ripple effect should not\n     * be dispatched. The ripple effect should only happen\n     * if the button is activated and the page is not scrolling.\n     *\n     * pointercancel is dispatched on a gesture when scrolling\n     * starts, so this lets us avoid having to listen for\n     * ion-content's scroll events.\n     */\n    doc.addEventListener('pointercancel', cancelActive, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = (ev) => {\n    if (ev.composedPath !== undefined) {\n        /**\n         * composedPath returns EventTarget[]. However,\n         * objects other than Element can be targets too.\n         * For example, AudioContext can be a target. In this\n         * case, we know that the event is a UIEvent so we\n         * can assume that the path will contain either Element\n         * or ShadowRoot.\n         */\n        const path = ev.composedPath();\n        for (let i = 0; i < path.length - 2; i++) {\n            const el = path[i];\n            if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n                return el;\n            }\n        }\n    }\n    else {\n        return ev.target.closest('.ion-activatable');\n    }\n};\nconst isInstant = (el) => {\n    return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = (el) => {\n    if (el.shadowRoot) {\n        const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n        if (ripple) {\n            return ripple;\n        }\n    }\n    return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\n\nexport { startTapClick };\n"], "mappings": ";;;;;;;;;;AAMA,IAAM,gBAAgB,CAAC,WAAW;AAC9B,MAAI,QAAQ,QAAW;AACnB;AAAA,EACJ;AACA,MAAI,gBAAgB;AACpB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,kBAAkB,OAAO,WAAW,YAAY,IAAI,KAAK,OAAO,WAAW,gBAAgB,IAAI;AACrG,QAAM,cAAc,oBAAI,QAAQ;AAChC,QAAM,eAAe,MAAM;AACvB,QAAI;AACA,mBAAa,WAAW;AAC5B,kBAAc;AACd,QAAI,gBAAgB;AAChB,sBAAgB,KAAK;AACrB,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,QAAM,cAAc,CAAC,OAAO;AAExB,QAAI,kBAAkB,GAAG,WAAW,GAAG;AACnC;AAAA,IACJ;AACA,wBAAoB,qBAAqB,EAAE,GAAG,EAAE;AAAA,EACpD;AACA,QAAM,YAAY,CAAC,OAAO;AACtB,wBAAoB,QAAW,EAAE;AAAA,EACrC;AACA,QAAM,sBAAsB,CAAC,IAAI,OAAO;AAEpC,QAAI,MAAM,OAAO,gBAAgB;AAC7B;AAAA,IACJ;AACA,QAAI;AACA,mBAAa,WAAW;AAC5B,kBAAc;AACd,UAAM,EAAE,GAAG,EAAE,IAAI,aAAa,EAAE;AAEhC,QAAI,gBAAgB;AAChB,UAAI,YAAY,IAAI,cAAc,GAAG;AACjC,cAAM,IAAI,MAAM,gBAAgB;AAAA,MACpC;AACA,UAAI,CAAC,eAAe,UAAU,SAAS,SAAS,GAAG;AAC/C,qBAAa,gBAAgB,GAAG,CAAC;AAAA,MACrC;AACA,sBAAgB,IAAI;AAAA,IACxB;AAEA,QAAI,IAAI;AACJ,YAAM,UAAU,YAAY,IAAI,EAAE;AAClC,UAAI,SAAS;AACT,qBAAa,OAAO;AACpB,oBAAY,OAAO,EAAE;AAAA,MACzB;AACA,SAAG,UAAU,OAAO,SAAS;AAC7B,YAAM,WAAW,MAAM;AACnB,qBAAa,IAAI,GAAG,CAAC;AACrB,sBAAc;AAAA,MAClB;AACA,UAAI,UAAU,EAAE,GAAG;AACf,iBAAS;AAAA,MACb,OACK;AACD,sBAAc,WAAW,UAAU,oBAAoB;AAAA,MAC3D;AAAA,IACJ;AACA,qBAAiB;AAAA,EACrB;AACA,QAAM,eAAe,CAAC,IAAI,GAAG,MAAM;AAC/B,oBAAgB,KAAK,IAAI;AACzB,OAAG,UAAU,IAAI,SAAS;AAC1B,QAAI,CAAC;AACD;AACJ,UAAM,eAAe,gBAAgB,EAAE;AACvC,QAAI,iBAAiB,MAAM;AACvB,mBAAa;AACb,qBAAe,aAAa,UAAU,GAAG,CAAC;AAAA,IAC9C;AAAA,EACJ;AACA,QAAM,eAAe,MAAM;AACvB,QAAI,iBAAiB,QAAW;AAC5B,mBAAa,KAAK,CAAC,WAAW,OAAO,CAAC;AACtC,qBAAe;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,kBAAkB,CAAC,WAAW;AAChC,iBAAa;AACb,UAAM,SAAS;AACf,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,UAAM,OAAO,qBAAqB,KAAK,IAAI,IAAI;AAC/C,QAAI,UAAU,OAAO,KAAK,CAAC,UAAU,MAAM,GAAG;AAC1C,YAAM,UAAU,WAAW,MAAM;AAC7B,eAAO,UAAU,OAAO,SAAS;AACjC,oBAAY,OAAO,MAAM;AAAA,MAC7B,GAAG,kBAAkB;AACrB,kBAAY,IAAI,QAAQ,OAAO;AAAA,IACnC,OACK;AACD,aAAO,UAAU,OAAO,SAAS;AAAA,IACrC;AAAA,EACJ;AACA,MAAI,iBAAiB,sBAAsB,YAAY;AACvD,MAAI,iBAAiB,eAAe,aAAa,IAAI;AACrD,MAAI,iBAAiB,aAAa,WAAW,IAAI;AAajD,MAAI,iBAAiB,iBAAiB,cAAc,IAAI;AAC5D;AAEA,IAAM,uBAAuB,CAAC,OAAO;AACjC,MAAI,GAAG,iBAAiB,QAAW;AAS/B,UAAM,OAAO,GAAG,aAAa;AAC7B,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACtC,YAAM,KAAK,KAAK,CAAC;AACjB,UAAI,EAAE,cAAc,eAAe,GAAG,UAAU,SAAS,iBAAiB,GAAG;AACzE,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ,OACK;AACD,WAAO,GAAG,OAAO,QAAQ,kBAAkB;AAAA,EAC/C;AACJ;AACA,IAAM,YAAY,CAAC,OAAO;AACtB,SAAO,GAAG,UAAU,SAAS,yBAAyB;AAC1D;AACA,IAAM,kBAAkB,CAAC,OAAO;AAC5B,MAAI,GAAG,YAAY;AACf,UAAM,SAAS,GAAG,WAAW,cAAc,mBAAmB;AAC9D,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,GAAG,cAAc,mBAAmB;AAC/C;AACA,IAAM,YAAY;AAClB,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;", "names": []}