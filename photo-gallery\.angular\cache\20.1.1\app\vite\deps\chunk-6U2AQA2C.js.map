{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/gesture-controller.js", "../../../../../../node_modules/@ionic/core/components/index3.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n    constructor() {\n        this.gestureId = 0;\n        this.requestedStart = new Map();\n        this.disabledGestures = new Map();\n        this.disabledScroll = new Set();\n    }\n    /**\n     * Creates a gesture delegate based on the GestureConfig passed\n     */\n    createGesture(config) {\n        var _a;\n        return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n    }\n    /**\n     * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n     */\n    createBlocker(opts = {}) {\n        return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n    }\n    start(gestureName, id, priority) {\n        if (!this.canStart(gestureName)) {\n            this.requestedStart.delete(id);\n            return false;\n        }\n        this.requestedStart.set(id, priority);\n        return true;\n    }\n    capture(gestureName, id, priority) {\n        if (!this.start(gestureName, id, priority)) {\n            return false;\n        }\n        const requestedStart = this.requestedStart;\n        let maxPriority = -1e4;\n        requestedStart.forEach((value) => {\n            maxPriority = Math.max(maxPriority, value);\n        });\n        if (maxPriority === priority) {\n            this.capturedId = id;\n            requestedStart.clear();\n            const event = new CustomEvent('ionGestureCaptured', { detail: { gestureName } });\n            document.dispatchEvent(event);\n            return true;\n        }\n        requestedStart.delete(id);\n        return false;\n    }\n    release(id) {\n        this.requestedStart.delete(id);\n        if (this.capturedId === id) {\n            this.capturedId = undefined;\n        }\n    }\n    disableGesture(gestureName, id) {\n        let set = this.disabledGestures.get(gestureName);\n        if (set === undefined) {\n            set = new Set();\n            this.disabledGestures.set(gestureName, set);\n        }\n        set.add(id);\n    }\n    enableGesture(gestureName, id) {\n        const set = this.disabledGestures.get(gestureName);\n        if (set !== undefined) {\n            set.delete(id);\n        }\n    }\n    disableScroll(id) {\n        this.disabledScroll.add(id);\n        if (this.disabledScroll.size === 1) {\n            document.body.classList.add(BACKDROP_NO_SCROLL);\n        }\n    }\n    enableScroll(id) {\n        this.disabledScroll.delete(id);\n        if (this.disabledScroll.size === 0) {\n            document.body.classList.remove(BACKDROP_NO_SCROLL);\n        }\n    }\n    canStart(gestureName) {\n        if (this.capturedId !== undefined) {\n            // a gesture already captured\n            return false;\n        }\n        if (this.isDisabled(gestureName)) {\n            return false;\n        }\n        return true;\n    }\n    isCaptured() {\n        return this.capturedId !== undefined;\n    }\n    isScrollDisabled() {\n        return this.disabledScroll.size > 0;\n    }\n    isDisabled(gestureName) {\n        const disabled = this.disabledGestures.get(gestureName);\n        if (disabled && disabled.size > 0) {\n            return true;\n        }\n        return false;\n    }\n    newID() {\n        this.gestureId++;\n        return this.gestureId;\n    }\n}\nclass GestureDelegate {\n    constructor(ctrl, id, name, priority, disableScroll) {\n        this.id = id;\n        this.name = name;\n        this.disableScroll = disableScroll;\n        this.priority = priority * 1000000 + id;\n        this.ctrl = ctrl;\n    }\n    canStart() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.canStart(this.name);\n    }\n    start() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.start(this.name, this.id, this.priority);\n    }\n    capture() {\n        if (!this.ctrl) {\n            return false;\n        }\n        const captured = this.ctrl.capture(this.name, this.id, this.priority);\n        if (captured && this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n        return captured;\n    }\n    release() {\n        if (this.ctrl) {\n            this.ctrl.release(this.id);\n            if (this.disableScroll) {\n                this.ctrl.enableScroll(this.id);\n            }\n        }\n    }\n    destroy() {\n        this.release();\n        this.ctrl = undefined;\n    }\n}\nclass BlockerDelegate {\n    constructor(ctrl, id, disable, disableScroll) {\n        this.id = id;\n        this.disable = disable;\n        this.disableScroll = disableScroll;\n        this.ctrl = ctrl;\n    }\n    block() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.disableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n    }\n    unblock() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.enableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.enableScroll(this.id);\n        }\n    }\n    destroy() {\n        this.unblock();\n        this.ctrl = undefined;\n    }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\n\nexport { BACKDROP_NO_SCROLL as B, GESTURE_CONTROLLER as G };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { G as GESTURE_CONTROLLER } from './gesture-controller.js';\n\nconst addEventListener = (el, // TODO(FW-2832): type\neventName, callback, opts) => {\n    // use event listener options when supported\n    // otherwise it's just a boolean for the \"capture\" arg\n    const listenerOpts = supportsPassive(el)\n        ? {\n            capture: false,\n            passive: !!opts.passive,\n        }\n        : false;\n    let add;\n    let remove;\n    if (el['__zone_symbol__addEventListener']) {\n        add = '__zone_symbol__addEventListener';\n        remove = '__zone_symbol__removeEventListener';\n    }\n    else {\n        add = 'addEventListener';\n        remove = 'removeEventListener';\n    }\n    el[add](eventName, callback, listenerOpts);\n    return () => {\n        el[remove](eventName, callback, listenerOpts);\n    };\n};\nconst supportsPassive = (node) => {\n    if (_sPassive === undefined) {\n        try {\n            const opts = Object.defineProperty({}, 'passive', {\n                get: () => {\n                    _sPassive = true;\n                },\n            });\n            node.addEventListener('optsTest', () => {\n                return;\n            }, opts);\n        }\n        catch (e) {\n            _sPassive = false;\n        }\n    }\n    return !!_sPassive;\n};\nlet _sPassive;\n\nconst MOUSE_WAIT = 2000;\n// TODO(FW-2832): types\nconst createPointerEvents = (el, pointerDown, pointerMove, pointerUp, options) => {\n    let rmTouchStart;\n    let rmTouchMove;\n    let rmTouchEnd;\n    let rmTouchCancel;\n    let rmMouseStart;\n    let rmMouseMove;\n    let rmMouseUp;\n    let lastTouchEvent = 0;\n    const handleTouchStart = (ev) => {\n        lastTouchEvent = Date.now() + MOUSE_WAIT;\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmTouchMove && pointerMove) {\n            rmTouchMove = addEventListener(el, 'touchmove', pointerMove, options);\n        }\n        /**\n         * Events are dispatched on the element that is tapped and bubble up to\n         * the reference element in the gesture. In the event that the element this\n         * event was first dispatched on is removed from the DOM, the event will no\n         * longer bubble up to our reference element. This leaves the gesture in an\n         * unusable state. To account for this, the touchend and touchcancel listeners\n         * should be added to the event target so that they still fire even if the target\n         * is removed from the DOM.\n         */\n        if (!rmTouchEnd) {\n            rmTouchEnd = addEventListener(ev.target, 'touchend', handleTouchEnd, options);\n        }\n        if (!rmTouchCancel) {\n            rmTouchCancel = addEventListener(ev.target, 'touchcancel', handleTouchEnd, options);\n        }\n    };\n    const handleMouseDown = (ev) => {\n        if (lastTouchEvent > Date.now()) {\n            return;\n        }\n        if (!pointerDown(ev)) {\n            return;\n        }\n        if (!rmMouseMove && pointerMove) {\n            rmMouseMove = addEventListener(getDocument(el), 'mousemove', pointerMove, options);\n        }\n        if (!rmMouseUp) {\n            rmMouseUp = addEventListener(getDocument(el), 'mouseup', handleMouseUp, options);\n        }\n    };\n    const handleTouchEnd = (ev) => {\n        stopTouch();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const handleMouseUp = (ev) => {\n        stopMouse();\n        if (pointerUp) {\n            pointerUp(ev);\n        }\n    };\n    const stopTouch = () => {\n        if (rmTouchMove) {\n            rmTouchMove();\n        }\n        if (rmTouchEnd) {\n            rmTouchEnd();\n        }\n        if (rmTouchCancel) {\n            rmTouchCancel();\n        }\n        rmTouchMove = rmTouchEnd = rmTouchCancel = undefined;\n    };\n    const stopMouse = () => {\n        if (rmMouseMove) {\n            rmMouseMove();\n        }\n        if (rmMouseUp) {\n            rmMouseUp();\n        }\n        rmMouseMove = rmMouseUp = undefined;\n    };\n    const stop = () => {\n        stopTouch();\n        stopMouse();\n    };\n    const enable = (isEnabled = true) => {\n        if (!isEnabled) {\n            if (rmTouchStart) {\n                rmTouchStart();\n            }\n            if (rmMouseStart) {\n                rmMouseStart();\n            }\n            rmTouchStart = rmMouseStart = undefined;\n            stop();\n        }\n        else {\n            if (!rmTouchStart) {\n                rmTouchStart = addEventListener(el, 'touchstart', handleTouchStart, options);\n            }\n            if (!rmMouseStart) {\n                rmMouseStart = addEventListener(el, 'mousedown', handleMouseDown, options);\n            }\n        }\n    };\n    const destroy = () => {\n        enable(false);\n        pointerUp = pointerMove = pointerDown = undefined;\n    };\n    return {\n        enable,\n        stop,\n        destroy,\n    };\n};\nconst getDocument = (node) => {\n    return node instanceof Document ? node : node.ownerDocument;\n};\n\nconst createPanRecognizer = (direction, thresh, maxAngle) => {\n    const radians = maxAngle * (Math.PI / 180);\n    const isDirX = direction === 'x';\n    const maxCosine = Math.cos(radians);\n    const threshold = thresh * thresh;\n    let startX = 0;\n    let startY = 0;\n    let dirty = false;\n    let isPan = 0;\n    return {\n        start(x, y) {\n            startX = x;\n            startY = y;\n            isPan = 0;\n            dirty = true;\n        },\n        detect(x, y) {\n            if (!dirty) {\n                return false;\n            }\n            const deltaX = x - startX;\n            const deltaY = y - startY;\n            const distance = deltaX * deltaX + deltaY * deltaY;\n            if (distance < threshold) {\n                return false;\n            }\n            const hypotenuse = Math.sqrt(distance);\n            const cosine = (isDirX ? deltaX : deltaY) / hypotenuse;\n            if (cosine > maxCosine) {\n                isPan = 1;\n            }\n            else if (cosine < -maxCosine) {\n                isPan = -1;\n            }\n            else {\n                isPan = 0;\n            }\n            dirty = false;\n            return true;\n        },\n        isGesture() {\n            return isPan !== 0;\n        },\n        getDirection() {\n            return isPan;\n        },\n    };\n};\n\n// TODO(FW-2832): types\nconst createGesture = (config) => {\n    let hasCapturedPan = false;\n    let hasStartedPan = false;\n    let hasFiredStart = true;\n    let isMoveQueued = false;\n    const finalConfig = Object.assign({ disableScroll: false, direction: 'x', gesturePriority: 0, passive: true, maxAngle: 40, threshold: 10 }, config);\n    const canStart = finalConfig.canStart;\n    const onWillStart = finalConfig.onWillStart;\n    const onStart = finalConfig.onStart;\n    const onEnd = finalConfig.onEnd;\n    const notCaptured = finalConfig.notCaptured;\n    const onMove = finalConfig.onMove;\n    const threshold = finalConfig.threshold;\n    const passive = finalConfig.passive;\n    const blurOnStart = finalConfig.blurOnStart;\n    const detail = {\n        type: 'pan',\n        startX: 0,\n        startY: 0,\n        startTime: 0,\n        currentX: 0,\n        currentY: 0,\n        velocityX: 0,\n        velocityY: 0,\n        deltaX: 0,\n        deltaY: 0,\n        currentTime: 0,\n        event: undefined,\n        data: undefined,\n    };\n    const pan = createPanRecognizer(finalConfig.direction, finalConfig.threshold, finalConfig.maxAngle);\n    const gesture = GESTURE_CONTROLLER.createGesture({\n        name: config.gestureName,\n        priority: config.gesturePriority,\n        disableScroll: config.disableScroll,\n    });\n    const pointerDown = (ev) => {\n        const timeStamp = now(ev);\n        if (hasStartedPan || !hasFiredStart) {\n            return false;\n        }\n        updateDetail(ev, detail);\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime = timeStamp;\n        detail.velocityX = detail.velocityY = detail.deltaX = detail.deltaY = 0;\n        detail.event = ev;\n        // Check if gesture can start\n        if (canStart && canStart(detail) === false) {\n            return false;\n        }\n        // Release fallback\n        gesture.release();\n        // Start gesture\n        if (!gesture.start()) {\n            return false;\n        }\n        hasStartedPan = true;\n        if (threshold === 0) {\n            return tryToCapturePan();\n        }\n        pan.start(detail.startX, detail.startY);\n        return true;\n    };\n    const pointerMove = (ev) => {\n        // fast path, if gesture is currently captured\n        // do minimum job to get user-land even dispatched\n        if (hasCapturedPan) {\n            if (!isMoveQueued && hasFiredStart) {\n                isMoveQueued = true;\n                calcGestureData(detail, ev);\n                requestAnimationFrame(fireOnMove);\n            }\n            return;\n        }\n        // gesture is currently being detected\n        calcGestureData(detail, ev);\n        if (pan.detect(detail.currentX, detail.currentY)) {\n            if (!pan.isGesture() || !tryToCapturePan()) {\n                abortGesture();\n            }\n        }\n    };\n    const fireOnMove = () => {\n        // Since fireOnMove is called inside a RAF, onEnd() might be called,\n        // we must double check hasCapturedPan\n        if (!hasCapturedPan) {\n            return;\n        }\n        isMoveQueued = false;\n        if (onMove) {\n            onMove(detail);\n        }\n    };\n    const tryToCapturePan = () => {\n        if (!gesture.capture()) {\n            return false;\n        }\n        hasCapturedPan = true;\n        hasFiredStart = false;\n        // reset start position since the real user-land event starts here\n        // If the pan detector threshold is big, not resetting the start position\n        // will cause a jump in the animation equal to the detector threshold.\n        // the array of positions used to calculate the gesture velocity does not\n        // need to be cleaned, more points in the positions array always results in a\n        // more accurate value of the velocity.\n        detail.startX = detail.currentX;\n        detail.startY = detail.currentY;\n        detail.startTime = detail.currentTime;\n        if (onWillStart) {\n            onWillStart(detail).then(fireOnStart);\n        }\n        else {\n            fireOnStart();\n        }\n        return true;\n    };\n    const blurActiveElement = () => {\n        if (typeof document !== 'undefined') {\n            const activeElement = document.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.blur) {\n                activeElement.blur();\n            }\n        }\n    };\n    const fireOnStart = () => {\n        if (blurOnStart) {\n            blurActiveElement();\n        }\n        if (onStart) {\n            onStart(detail);\n        }\n        hasFiredStart = true;\n    };\n    const reset = () => {\n        hasCapturedPan = false;\n        hasStartedPan = false;\n        isMoveQueued = false;\n        hasFiredStart = true;\n        gesture.release();\n    };\n    // END *************************\n    const pointerUp = (ev) => {\n        const tmpHasCaptured = hasCapturedPan;\n        const tmpHasFiredStart = hasFiredStart;\n        reset();\n        if (!tmpHasFiredStart) {\n            return;\n        }\n        calcGestureData(detail, ev);\n        // Try to capture press\n        if (tmpHasCaptured) {\n            if (onEnd) {\n                onEnd(detail);\n            }\n            return;\n        }\n        // Not captured any event\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    const pointerEvents = createPointerEvents(finalConfig.el, pointerDown, pointerMove, pointerUp, {\n        passive,\n    });\n    const abortGesture = () => {\n        reset();\n        pointerEvents.stop();\n        if (notCaptured) {\n            notCaptured(detail);\n        }\n    };\n    return {\n        enable(enable = true) {\n            if (!enable) {\n                if (hasCapturedPan) {\n                    pointerUp(undefined);\n                }\n                reset();\n            }\n            pointerEvents.enable(enable);\n        },\n        destroy() {\n            gesture.destroy();\n            pointerEvents.destroy();\n        },\n    };\n};\nconst calcGestureData = (detail, ev) => {\n    if (!ev) {\n        return;\n    }\n    const prevX = detail.currentX;\n    const prevY = detail.currentY;\n    const prevT = detail.currentTime;\n    updateDetail(ev, detail);\n    const currentX = detail.currentX;\n    const currentY = detail.currentY;\n    const timestamp = (detail.currentTime = now(ev));\n    const timeDelta = timestamp - prevT;\n    if (timeDelta > 0 && timeDelta < 100) {\n        const velocityX = (currentX - prevX) / timeDelta;\n        const velocityY = (currentY - prevY) / timeDelta;\n        detail.velocityX = velocityX * 0.7 + detail.velocityX * 0.3;\n        detail.velocityY = velocityY * 0.7 + detail.velocityY * 0.3;\n    }\n    detail.deltaX = currentX - detail.startX;\n    detail.deltaY = currentY - detail.startY;\n    detail.event = ev;\n};\nconst updateDetail = (ev, detail) => {\n    // get X coordinates for either a mouse click\n    // or a touch depending on the given event\n    let x = 0;\n    let y = 0;\n    if (ev) {\n        const changedTouches = ev.changedTouches;\n        if (changedTouches && changedTouches.length > 0) {\n            const touch = changedTouches[0];\n            x = touch.clientX;\n            y = touch.clientY;\n        }\n        else if (ev.pageX !== undefined) {\n            x = ev.pageX;\n            y = ev.pageY;\n        }\n    }\n    detail.currentX = x;\n    detail.currentY = y;\n};\nconst now = (ev) => {\n    return ev.timeStamp || Date.now();\n};\n\nexport { GESTURE_CONTROLLER, createGesture };\n"], "mappings": ";AAGA,IAAM,oBAAN,MAAwB;AAAA,EACpB,cAAc;AACV,SAAK,YAAY;AACjB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,oBAAI,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ;AAClB,QAAI;AACJ,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,OAAO,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,CAAC,GAAG;AACrB,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,aAAa;AAAA,EACrF;AAAA,EACA,MAAM,aAAa,IAAI,UAAU;AAC7B,QAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC7B,WAAK,eAAe,OAAO,EAAE;AAC7B,aAAO;AAAA,IACX;AACA,SAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,aAAa,IAAI,UAAU;AAC/B,QAAI,CAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,GAAG;AACxC,aAAO;AAAA,IACX;AACA,UAAM,iBAAiB,KAAK;AAC5B,QAAI,cAAc;AAClB,mBAAe,QAAQ,CAAC,UAAU;AAC9B,oBAAc,KAAK,IAAI,aAAa,KAAK;AAAA,IAC7C,CAAC;AACD,QAAI,gBAAgB,UAAU;AAC1B,WAAK,aAAa;AAClB,qBAAe,MAAM;AACrB,YAAM,QAAQ,IAAI,YAAY,sBAAsB,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;AAC/E,eAAS,cAAc,KAAK;AAC5B,aAAO;AAAA,IACX;AACA,mBAAe,OAAO,EAAE;AACxB,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,IAAI;AACR,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,IAAI;AACxB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,eAAe,aAAa,IAAI;AAC5B,QAAI,MAAM,KAAK,iBAAiB,IAAI,WAAW;AAC/C,QAAI,QAAQ,QAAW;AACnB,YAAM,oBAAI,IAAI;AACd,WAAK,iBAAiB,IAAI,aAAa,GAAG;AAAA,IAC9C;AACA,QAAI,IAAI,EAAE;AAAA,EACd;AAAA,EACA,cAAc,aAAa,IAAI;AAC3B,UAAM,MAAM,KAAK,iBAAiB,IAAI,WAAW;AACjD,QAAI,QAAQ,QAAW;AACnB,UAAI,OAAO,EAAE;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,cAAc,IAAI;AACd,SAAK,eAAe,IAAI,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAAG;AAChC,eAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,aAAa,IAAI;AACb,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAChC,eAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,IACrD;AAAA,EACJ;AAAA,EACA,SAAS,aAAa;AAClB,QAAI,KAAK,eAAe,QAAW;AAE/B,aAAO;AAAA,IACX;AACA,QAAI,KAAK,WAAW,WAAW,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa;AACT,WAAO,KAAK,eAAe;AAAA,EAC/B;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK,eAAe,OAAO;AAAA,EACtC;AAAA,EACA,WAAW,aAAa;AACpB,UAAM,WAAW,KAAK,iBAAiB,IAAI,WAAW;AACtD,QAAI,YAAY,SAAS,OAAO,GAAG;AAC/B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,SAAK;AACL,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,IAAI,MAAM,UAAU,eAAe;AACjD,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,WAAW,WAAW,MAAU;AACrC,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW;AACP,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,WAAO,KAAK,KAAK,SAAS,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,WAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,UAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AACpE,QAAI,YAAY,KAAK,eAAe;AAChC,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,QAAQ,KAAK,EAAE;AACzB,UAAI,KAAK,eAAe;AACpB,aAAK,KAAK,aAAa,KAAK,EAAE;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,IAAI,SAAS,eAAe;AAC1C,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,iBAAW,WAAW,KAAK,SAAS;AAChC,aAAK,KAAK,eAAe,SAAS,KAAK,EAAE;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,iBAAW,WAAW,KAAK,SAAS;AAChC,aAAK,KAAK,cAAc,SAAS,KAAK,EAAE;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,aAAa,KAAK,EAAE;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,IAAI,kBAAkB;;;AC3LjD,IAAM,mBAAmB,CAAC,IAC1B,WAAW,UAAU,SAAS;AAG1B,QAAM,eAAe,gBAAgB,EAAE,IACjC;AAAA,IACE,SAAS;AAAA,IACT,SAAS,CAAC,CAAC,KAAK;AAAA,EACpB,IACE;AACN,MAAI;AACJ,MAAI;AACJ,MAAI,GAAG,iCAAiC,GAAG;AACvC,UAAM;AACN,aAAS;AAAA,EACb,OACK;AACD,UAAM;AACN,aAAS;AAAA,EACb;AACA,KAAG,GAAG,EAAE,WAAW,UAAU,YAAY;AACzC,SAAO,MAAM;AACT,OAAG,MAAM,EAAE,WAAW,UAAU,YAAY;AAAA,EAChD;AACJ;AACA,IAAM,kBAAkB,CAAC,SAAS;AAC9B,MAAI,cAAc,QAAW;AACzB,QAAI;AACA,YAAM,OAAO,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,QAC9C,KAAK,MAAM;AACP,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,WAAK,iBAAiB,YAAY,MAAM;AACpC;AAAA,MACJ,GAAG,IAAI;AAAA,IACX,SACO,GAAG;AACN,kBAAY;AAAA,IAChB;AAAA,EACJ;AACA,SAAO,CAAC,CAAC;AACb;AACA,IAAI;AAEJ,IAAM,aAAa;AAEnB,IAAM,sBAAsB,CAAC,IAAI,aAAa,aAAa,WAAW,YAAY;AAC9E,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,iBAAiB;AACrB,QAAM,mBAAmB,CAAC,OAAO;AAC7B,qBAAiB,KAAK,IAAI,IAAI;AAC9B,QAAI,CAAC,YAAY,EAAE,GAAG;AAClB;AAAA,IACJ;AACA,QAAI,CAAC,eAAe,aAAa;AAC7B,oBAAc,iBAAiB,IAAI,aAAa,aAAa,OAAO;AAAA,IACxE;AAUA,QAAI,CAAC,YAAY;AACb,mBAAa,iBAAiB,GAAG,QAAQ,YAAY,gBAAgB,OAAO;AAAA,IAChF;AACA,QAAI,CAAC,eAAe;AAChB,sBAAgB,iBAAiB,GAAG,QAAQ,eAAe,gBAAgB,OAAO;AAAA,IACtF;AAAA,EACJ;AACA,QAAM,kBAAkB,CAAC,OAAO;AAC5B,QAAI,iBAAiB,KAAK,IAAI,GAAG;AAC7B;AAAA,IACJ;AACA,QAAI,CAAC,YAAY,EAAE,GAAG;AAClB;AAAA,IACJ;AACA,QAAI,CAAC,eAAe,aAAa;AAC7B,oBAAc,iBAAiB,YAAY,EAAE,GAAG,aAAa,aAAa,OAAO;AAAA,IACrF;AACA,QAAI,CAAC,WAAW;AACZ,kBAAY,iBAAiB,YAAY,EAAE,GAAG,WAAW,eAAe,OAAO;AAAA,IACnF;AAAA,EACJ;AACA,QAAM,iBAAiB,CAAC,OAAO;AAC3B,cAAU;AACV,QAAI,WAAW;AACX,gBAAU,EAAE;AAAA,IAChB;AAAA,EACJ;AACA,QAAM,gBAAgB,CAAC,OAAO;AAC1B,cAAU;AACV,QAAI,WAAW;AACX,gBAAU,EAAE;AAAA,IAChB;AAAA,EACJ;AACA,QAAM,YAAY,MAAM;AACpB,QAAI,aAAa;AACb,kBAAY;AAAA,IAChB;AACA,QAAI,YAAY;AACZ,iBAAW;AAAA,IACf;AACA,QAAI,eAAe;AACf,oBAAc;AAAA,IAClB;AACA,kBAAc,aAAa,gBAAgB;AAAA,EAC/C;AACA,QAAM,YAAY,MAAM;AACpB,QAAI,aAAa;AACb,kBAAY;AAAA,IAChB;AACA,QAAI,WAAW;AACX,gBAAU;AAAA,IACd;AACA,kBAAc,YAAY;AAAA,EAC9B;AACA,QAAM,OAAO,MAAM;AACf,cAAU;AACV,cAAU;AAAA,EACd;AACA,QAAM,SAAS,CAAC,YAAY,SAAS;AACjC,QAAI,CAAC,WAAW;AACZ,UAAI,cAAc;AACd,qBAAa;AAAA,MACjB;AACA,UAAI,cAAc;AACd,qBAAa;AAAA,MACjB;AACA,qBAAe,eAAe;AAC9B,WAAK;AAAA,IACT,OACK;AACD,UAAI,CAAC,cAAc;AACf,uBAAe,iBAAiB,IAAI,cAAc,kBAAkB,OAAO;AAAA,MAC/E;AACA,UAAI,CAAC,cAAc;AACf,uBAAe,iBAAiB,IAAI,aAAa,iBAAiB,OAAO;AAAA,MAC7E;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,UAAU,MAAM;AAClB,WAAO,KAAK;AACZ,gBAAY,cAAc,cAAc;AAAA,EAC5C;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,cAAc,CAAC,SAAS;AAC1B,SAAO,gBAAgB,WAAW,OAAO,KAAK;AAClD;AAEA,IAAM,sBAAsB,CAAC,WAAW,QAAQ,aAAa;AACzD,QAAM,UAAU,YAAY,KAAK,KAAK;AACtC,QAAM,SAAS,cAAc;AAC7B,QAAM,YAAY,KAAK,IAAI,OAAO;AAClC,QAAM,YAAY,SAAS;AAC3B,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,SAAO;AAAA,IACH,MAAM,GAAG,GAAG;AACR,eAAS;AACT,eAAS;AACT,cAAQ;AACR,cAAQ;AAAA,IACZ;AAAA,IACA,OAAO,GAAG,GAAG;AACT,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,YAAM,SAAS,IAAI;AACnB,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW,SAAS,SAAS,SAAS;AAC5C,UAAI,WAAW,WAAW;AACtB,eAAO;AAAA,MACX;AACA,YAAM,aAAa,KAAK,KAAK,QAAQ;AACrC,YAAM,UAAU,SAAS,SAAS,UAAU;AAC5C,UAAI,SAAS,WAAW;AACpB,gBAAQ;AAAA,MACZ,WACS,SAAS,CAAC,WAAW;AAC1B,gBAAQ;AAAA,MACZ,OACK;AACD,gBAAQ;AAAA,MACZ;AACA,cAAQ;AACR,aAAO;AAAA,IACX;AAAA,IACA,YAAY;AACR,aAAO,UAAU;AAAA,IACrB;AAAA,IACA,eAAe;AACX,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAGA,IAAM,gBAAgB,CAAC,WAAW;AAC9B,MAAI,iBAAiB;AACrB,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AACpB,MAAI,eAAe;AACnB,QAAM,cAAc,OAAO,OAAO,EAAE,eAAe,OAAO,WAAW,KAAK,iBAAiB,GAAG,SAAS,MAAM,UAAU,IAAI,WAAW,GAAG,GAAG,MAAM;AAClJ,QAAM,WAAW,YAAY;AAC7B,QAAM,cAAc,YAAY;AAChC,QAAM,UAAU,YAAY;AAC5B,QAAM,QAAQ,YAAY;AAC1B,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS,YAAY;AAC3B,QAAM,YAAY,YAAY;AAC9B,QAAM,UAAU,YAAY;AAC5B,QAAM,cAAc,YAAY;AAChC,QAAM,SAAS;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,EACV;AACA,QAAM,MAAM,oBAAoB,YAAY,WAAW,YAAY,WAAW,YAAY,QAAQ;AAClG,QAAM,UAAU,mBAAmB,cAAc;AAAA,IAC7C,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,eAAe,OAAO;AAAA,EAC1B,CAAC;AACD,QAAM,cAAc,CAAC,OAAO;AACxB,UAAM,YAAY,IAAI,EAAE;AACxB,QAAI,iBAAiB,CAAC,eAAe;AACjC,aAAO;AAAA,IACX;AACA,iBAAa,IAAI,MAAM;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,YAAY,OAAO,cAAc;AACxC,WAAO,YAAY,OAAO,YAAY,OAAO,SAAS,OAAO,SAAS;AACtE,WAAO,QAAQ;AAEf,QAAI,YAAY,SAAS,MAAM,MAAM,OAAO;AACxC,aAAO;AAAA,IACX;AAEA,YAAQ,QAAQ;AAEhB,QAAI,CAAC,QAAQ,MAAM,GAAG;AAClB,aAAO;AAAA,IACX;AACA,oBAAgB;AAChB,QAAI,cAAc,GAAG;AACjB,aAAO,gBAAgB;AAAA,IAC3B;AACA,QAAI,MAAM,OAAO,QAAQ,OAAO,MAAM;AACtC,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAAC,OAAO;AAGxB,QAAI,gBAAgB;AAChB,UAAI,CAAC,gBAAgB,eAAe;AAChC,uBAAe;AACf,wBAAgB,QAAQ,EAAE;AAC1B,8BAAsB,UAAU;AAAA,MACpC;AACA;AAAA,IACJ;AAEA,oBAAgB,QAAQ,EAAE;AAC1B,QAAI,IAAI,OAAO,OAAO,UAAU,OAAO,QAAQ,GAAG;AAC9C,UAAI,CAAC,IAAI,UAAU,KAAK,CAAC,gBAAgB,GAAG;AACxC,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,aAAa,MAAM;AAGrB,QAAI,CAAC,gBAAgB;AACjB;AAAA,IACJ;AACA,mBAAe;AACf,QAAI,QAAQ;AACR,aAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACA,QAAM,kBAAkB,MAAM;AAC1B,QAAI,CAAC,QAAQ,QAAQ,GAAG;AACpB,aAAO;AAAA,IACX;AACA,qBAAiB;AACjB,oBAAgB;AAOhB,WAAO,SAAS,OAAO;AACvB,WAAO,SAAS,OAAO;AACvB,WAAO,YAAY,OAAO;AAC1B,QAAI,aAAa;AACb,kBAAY,MAAM,EAAE,KAAK,WAAW;AAAA,IACxC,OACK;AACD,kBAAY;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AACA,QAAM,oBAAoB,MAAM;AAC5B,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,gBAAgB,SAAS;AAC/B,UAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM;AAClF,sBAAc,KAAK;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,cAAc,MAAM;AACtB,QAAI,aAAa;AACb,wBAAkB;AAAA,IACtB;AACA,QAAI,SAAS;AACT,cAAQ,MAAM;AAAA,IAClB;AACA,oBAAgB;AAAA,EACpB;AACA,QAAM,QAAQ,MAAM;AAChB,qBAAiB;AACjB,oBAAgB;AAChB,mBAAe;AACf,oBAAgB;AAChB,YAAQ,QAAQ;AAAA,EACpB;AAEA,QAAM,YAAY,CAAC,OAAO;AACtB,UAAM,iBAAiB;AACvB,UAAM,mBAAmB;AACzB,UAAM;AACN,QAAI,CAAC,kBAAkB;AACnB;AAAA,IACJ;AACA,oBAAgB,QAAQ,EAAE;AAE1B,QAAI,gBAAgB;AAChB,UAAI,OAAO;AACP,cAAM,MAAM;AAAA,MAChB;AACA;AAAA,IACJ;AAEA,QAAI,aAAa;AACb,kBAAY,MAAM;AAAA,IACtB;AAAA,EACJ;AACA,QAAM,gBAAgB,oBAAoB,YAAY,IAAI,aAAa,aAAa,WAAW;AAAA,IAC3F;AAAA,EACJ,CAAC;AACD,QAAM,eAAe,MAAM;AACvB,UAAM;AACN,kBAAc,KAAK;AACnB,QAAI,aAAa;AACb,kBAAY,MAAM;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AAAA,IACH,OAAO,SAAS,MAAM;AAClB,UAAI,CAAC,QAAQ;AACT,YAAI,gBAAgB;AAChB,oBAAU,MAAS;AAAA,QACvB;AACA,cAAM;AAAA,MACV;AACA,oBAAc,OAAO,MAAM;AAAA,IAC/B;AAAA,IACA,UAAU;AACN,cAAQ,QAAQ;AAChB,oBAAc,QAAQ;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAM,kBAAkB,CAAC,QAAQ,OAAO;AACpC,MAAI,CAAC,IAAI;AACL;AAAA,EACJ;AACA,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,OAAO;AACrB,QAAM,QAAQ,OAAO;AACrB,eAAa,IAAI,MAAM;AACvB,QAAM,WAAW,OAAO;AACxB,QAAM,WAAW,OAAO;AACxB,QAAM,YAAa,OAAO,cAAc,IAAI,EAAE;AAC9C,QAAM,YAAY,YAAY;AAC9B,MAAI,YAAY,KAAK,YAAY,KAAK;AAClC,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,aAAa,WAAW,SAAS;AACvC,WAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AACxD,WAAO,YAAY,YAAY,MAAM,OAAO,YAAY;AAAA,EAC5D;AACA,SAAO,SAAS,WAAW,OAAO;AAClC,SAAO,SAAS,WAAW,OAAO;AAClC,SAAO,QAAQ;AACnB;AACA,IAAM,eAAe,CAAC,IAAI,WAAW;AAGjC,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACJ,UAAM,iBAAiB,GAAG;AAC1B,QAAI,kBAAkB,eAAe,SAAS,GAAG;AAC7C,YAAM,QAAQ,eAAe,CAAC;AAC9B,UAAI,MAAM;AACV,UAAI,MAAM;AAAA,IACd,WACS,GAAG,UAAU,QAAW;AAC7B,UAAI,GAAG;AACP,UAAI,GAAG;AAAA,IACX;AAAA,EACJ;AACA,SAAO,WAAW;AAClB,SAAO,WAAW;AACtB;AACA,IAAM,MAAM,CAAC,OAAO;AAChB,SAAO,GAAG,aAAa,KAAK,IAAI;AACpC;", "names": []}