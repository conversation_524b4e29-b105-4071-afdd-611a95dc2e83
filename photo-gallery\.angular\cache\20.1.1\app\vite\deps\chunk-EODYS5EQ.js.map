{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/hardware-back-button.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index9.js';\nimport { c as config, a as printIonError } from './index4.js';\n\n/**\n * CloseWatcher is a newer API that lets\n * use detect the hardware back button event\n * in a web browser: https://caniuse.com/?search=closewatcher\n * However, not every browser supports it yet.\n *\n * This needs to be a function so that we can\n * check the config once it has been set.\n * Otherwise, this code would be evaluated the\n * moment this file is evaluated which could be\n * before the config is set.\n */\nconst shouldUseCloseWatcher = () => config.get('experimentalCloseWatcher', false) && win !== undefined && 'CloseWatcher' in win;\n/**\n * When hardwareBackButton: false in config,\n * we need to make sure we also block the default\n * webview behavior. If we don't then it will be\n * possible for users to navigate backward while\n * an overlay is still open. Additionally, it will\n * give the appearance that the hardwareBackButton\n * config is not working as the page transition\n * will still happen.\n */\nconst blockHardwareBackButton = () => {\n    document.addEventListener('backbutton', () => { }); // eslint-disable-line\n};\nconst startHardwareBackButton = () => {\n    const doc = document;\n    let busy = false;\n    const backButtonCallback = () => {\n        if (busy) {\n            return;\n        }\n        let index = 0;\n        let handlers = [];\n        const ev = new CustomEvent('ionBackButton', {\n            bubbles: false,\n            detail: {\n                register(priority, handler) {\n                    handlers.push({ priority, handler, id: index++ });\n                },\n            },\n        });\n        doc.dispatchEvent(ev);\n        const executeAction = async (handlerRegister) => {\n            try {\n                if (handlerRegister === null || handlerRegister === void 0 ? void 0 : handlerRegister.handler) {\n                    const result = handlerRegister.handler(processHandlers);\n                    if (result != null) {\n                        await result;\n                    }\n                }\n            }\n            catch (e) {\n                printIonError('[ion-app] - Exception in startHardwareBackButton:', e);\n            }\n        };\n        const processHandlers = () => {\n            if (handlers.length > 0) {\n                let selectedHandler = {\n                    priority: Number.MIN_SAFE_INTEGER,\n                    handler: () => undefined,\n                    id: -1,\n                };\n                handlers.forEach((handler) => {\n                    if (handler.priority >= selectedHandler.priority) {\n                        selectedHandler = handler;\n                    }\n                });\n                busy = true;\n                handlers = handlers.filter((handler) => handler.id !== selectedHandler.id);\n                executeAction(selectedHandler).then(() => (busy = false));\n            }\n        };\n        processHandlers();\n    };\n    /**\n     * If the CloseWatcher is defined then\n     * we don't want to also listen for the native\n     * backbutton event otherwise we may get duplicate\n     * events firing.\n     */\n    if (shouldUseCloseWatcher()) {\n        let watcher;\n        const configureWatcher = () => {\n            watcher === null || watcher === void 0 ? void 0 : watcher.destroy();\n            watcher = new win.CloseWatcher();\n            /**\n             * Once a close request happens\n             * the watcher gets destroyed.\n             * As a result, we need to re-configure\n             * the watcher so we can respond to other\n             * close requests.\n             */\n            watcher.onclose = () => {\n                backButtonCallback();\n                configureWatcher();\n            };\n        };\n        configureWatcher();\n    }\n    else {\n        doc.addEventListener('backbutton', backButtonCallback);\n    }\n};\nconst OVERLAY_BACK_BUTTON_PRIORITY = 100;\nconst MENU_BACK_BUTTON_PRIORITY = 99; // 1 less than overlay priority since menu is displayed behind overlays\n\nexport { MENU_BACK_BUTTON_PRIORITY, OVERLAY_BACK_BUTTON_PRIORITY, blockHardwareBackButton, shouldUseCloseWatcher, startHardwareBackButton };\n"], "mappings": ";;;;;;;;;;;;AAkBA,IAAM,wBAAwB,MAAM,OAAO,IAAI,4BAA4B,KAAK,KAAK,QAAQ,UAAa,kBAAkB;AAW5H,IAAM,0BAA0B,MAAM;AAClC,WAAS,iBAAiB,cAAc,MAAM;AAAA,EAAE,CAAC;AACrD;AACA,IAAM,0BAA0B,MAAM;AAClC,QAAM,MAAM;AACZ,MAAI,OAAO;AACX,QAAM,qBAAqB,MAAM;AAC7B,QAAI,MAAM;AACN;AAAA,IACJ;AACA,QAAI,QAAQ;AACZ,QAAI,WAAW,CAAC;AAChB,UAAM,KAAK,IAAI,YAAY,iBAAiB;AAAA,MACxC,SAAS;AAAA,MACT,QAAQ;AAAA,QACJ,SAAS,UAAU,SAAS;AACxB,mBAAS,KAAK,EAAE,UAAU,SAAS,IAAI,QAAQ,CAAC;AAAA,QACpD;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,cAAc,EAAE;AACpB,UAAM,gBAAgB,CAAO,oBAAoB;AAC7C,UAAI;AACA,YAAI,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,SAAS;AAC3F,gBAAM,SAAS,gBAAgB,QAAQ,eAAe;AACtD,cAAI,UAAU,MAAM;AAChB,kBAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ,SACO,GAAG;AACN,sBAAc,qDAAqD,CAAC;AAAA,MACxE;AAAA,IACJ;AACA,UAAM,kBAAkB,MAAM;AAC1B,UAAI,SAAS,SAAS,GAAG;AACrB,YAAI,kBAAkB;AAAA,UAClB,UAAU,OAAO;AAAA,UACjB,SAAS,MAAM;AAAA,UACf,IAAI;AAAA,QACR;AACA,iBAAS,QAAQ,CAAC,YAAY;AAC1B,cAAI,QAAQ,YAAY,gBAAgB,UAAU;AAC9C,8BAAkB;AAAA,UACtB;AAAA,QACJ,CAAC;AACD,eAAO;AACP,mBAAW,SAAS,OAAO,CAAC,YAAY,QAAQ,OAAO,gBAAgB,EAAE;AACzE,sBAAc,eAAe,EAAE,KAAK,MAAO,OAAO,KAAM;AAAA,MAC5D;AAAA,IACJ;AACA,oBAAgB;AAAA,EACpB;AAOA,MAAI,sBAAsB,GAAG;AACzB,QAAI;AACJ,UAAM,mBAAmB,MAAM;AAC3B,kBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AAClE,gBAAU,IAAI,IAAI,aAAa;AAQ/B,cAAQ,UAAU,MAAM;AACpB,2BAAmB;AACnB,yBAAiB;AAAA,MACrB;AAAA,IACJ;AACA,qBAAiB;AAAA,EACrB,OACK;AACD,QAAI,iBAAiB,cAAc,kBAAkB;AAAA,EACzD;AACJ;AACA,IAAM,+BAA+B;AACrC,IAAM,4BAA4B;", "names": []}