import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-header', // Your header component's selector
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit {

  @Input() title: string = 'Default Title'; // Input for the header title
  @Input() showMenuButton: boolean = false; // Input to control menu button visibility
  @Input() showBackButton: boolean = false; // Input to control back button visibility
  @Input() defaultHref: string = '/'; // Input for back button's default href

  constructor() { }

  ngOnInit() {}

}